#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Final para JavaScript Ofuscado
Reconstrói o código original baseado na análise das strings
"""

import re
import json
from typing import List, Dict, Any

def read_file(filename: str) -> str:
    """Lê o arquivo"""
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_complete_string_array(code: str) -> List[str]:
    """Extrai o array completo de strings do código"""
    # Procura pelo padrão específico do array de strings
    # Geralmente está em uma função que retorna um array
    pattern = r'const\s+\w+\s*=\s*\[(.*?)\];'
    matches = re.findall(pattern, code, re.DOTALL)
    
    all_strings = []
    for match in matches:
        # Extrai strings do match
        strings = re.findall(r'[\'"]([^\'"]*)[\'"]', match)
        if len(strings) > 100:  # Considera apenas arrays grandes
            all_strings.extend(strings)
    
    # Se não encontrou, tenta uma abordagem mais ampla
    if not all_strings:
        # Procura por qualquer array grande de strings
        large_array_pattern = r'\[([\'"][^\'"]*[\'"](?:\s*,\s*[\'"][^\'"]*[\'"]){100,})\]'
        matches = re.findall(large_array_pattern, code, re.DOTALL)
        for match in matches:
            strings = re.findall(r'[\'"]([^\'"]*)[\'"]', match)
            all_strings.extend(strings)
    
    return all_strings

def reconstruct_strings(fragments: List[str]) -> List[str]:
    """Reconstrói strings completas a partir dos fragmentos"""
    reconstructed = []
    
    # Agrupa fragmentos que parecem pertencer à mesma string
    i = 0
    while i < len(fragments):
        current = fragments[i]
        
        # Verifica se o próximo fragmento pode ser uma continuação
        if i + 1 < len(fragments):
            next_frag = fragments[i + 1]
            
            # Se o fragmento atual termina de forma incompleta e o próximo começa de forma incompleta
            if (not current.endswith(' ') and not next_frag.startswith(' ') and 
                len(current) < 10 and len(next_frag) < 10):
                # Tenta combinar
                combined = current + next_frag
                reconstructed.append(combined)
                i += 2
                continue
        
        reconstructed.append(current)
        i += 1
    
    return reconstructed

def analyze_content_type(strings: List[str]) -> Dict[str, Any]:
    """Analisa o tipo de conteúdo baseado nas strings"""
    analysis = {
        'type': 'unknown',
        'framework': None,
        'purpose': None,
        'technologies': [],
        'suspicious': False
    }
    
    # Conta ocorrências de tecnologias
    tech_counts = {
        'react': 0,
        'mui': 0,
        'css': 0,
        'html': 0,
        'javascript': 0
    }
    
    for string in strings:
        string_lower = string.lower()
        
        # React/Material-UI
        if 'mui' in string or '.Mui' in string:
            tech_counts['mui'] += 1
        
        # CSS
        if any(css_term in string for css_term in ['px', 'em', 'rem', 'color:', 'font-', 'margin', 'padding']):
            tech_counts['css'] += 1
        
        # HTML
        if any(html_term in string for html_term in ['<div', '<span', '<p>', '<h1', '<h2', 'class=', 'id=']):
            tech_counts['html'] += 1
        
        # JavaScript
        if any(js_term in string for js_term in ['function', 'var ', 'let ', 'const ', 'return']):
            tech_counts['javascript'] += 1
    
    # Determina o tipo principal
    if tech_counts['mui'] > 10:
        analysis['framework'] = 'Material-UI (React)'
        analysis['type'] = 'web_application'
    
    if tech_counts['css'] > 50:
        analysis['technologies'].append('CSS')
    
    if tech_counts['html'] > 20:
        analysis['technologies'].append('HTML')
    
    if tech_counts['javascript'] > 10:
        analysis['technologies'].append('JavaScript')
    
    # Verifica se é suspeito
    suspicious_terms = ['eval', 'document.write', 'innerHTML', 'outerHTML', 'script']
    for string in strings:
        if any(term in string.lower() for term in suspicious_terms):
            analysis['suspicious'] = True
            break
    
    return analysis

def generate_readable_summary(strings: List[str], analysis: Dict[str, Any]) -> str:
    """Gera um resumo legível do conteúdo"""
    summary = []
    
    summary.append("=== RESUMO DO CÓDIGO DECODIFICADO ===\n")
    
    # Tipo de aplicação
    if analysis['framework']:
        summary.append(f"Framework detectado: {analysis['framework']}")
    
    if analysis['technologies']:
        summary.append(f"Tecnologias: {', '.join(analysis['technologies'])}")
    
    summary.append(f"Tipo: {analysis['type']}")
    
    if analysis['suspicious']:
        summary.append("⚠️  ATENÇÃO: Código contém padrões potencialmente suspeitos")
    
    summary.append(f"\nTotal de strings decodificadas: {len(strings)}")
    
    # Categoriza strings por tipo
    categories = {
        'CSS Properties': [],
        'HTML Elements': [],
        'Class Names': [],
        'URLs/Paths': [],
        'Text Content': [],
        'Configuration': []
    }
    
    for string in strings[:200]:  # Limita para evitar spam
        if any(css in string for css in ['px', 'em', 'color:', 'font-', 'margin:', 'padding:']):
            categories['CSS Properties'].append(string)
        elif any(html in string for html in ['<', '>', 'div', 'span', 'class=']):
            categories['HTML Elements'].append(string)
        elif string.startswith('.') or 'Mui' in string:
            categories['Class Names'].append(string)
        elif '/' in string or 'http' in string:
            categories['URLs/Paths'].append(string)
        elif len(string) > 10 and any(c.isalpha() for c in string):
            categories['Text Content'].append(string)
        else:
            categories['Configuration'].append(string)
    
    summary.append("\n=== CATEGORIAS DE CONTEÚDO ===")
    for category, items in categories.items():
        if items:
            summary.append(f"\n{category} ({len(items)} itens):")
            for item in items[:10]:  # Mostra até 10 por categoria
                summary.append(f"  - {item}")
            if len(items) > 10:
                summary.append(f"  ... e mais {len(items) - 10}")
    
    return '\n'.join(summary)

def main():
    print("=== DECODER FINAL PARA JAVASCRIPT OFUSCADO ===\n")
    
    # Carrega as strings já extraídas
    try:
        with open('decoded_strings.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            strings = data['meaningful_strings']
    except FileNotFoundError:
        print("Arquivo decoded_strings.json não encontrado. Execute advanced_decoder.py primeiro.")
        return
    
    print(f"Carregadas {len(strings)} strings decodificadas")
    
    # Reconstrói strings fragmentadas
    print("\n=== RECONSTRUINDO STRINGS ===")
    reconstructed = reconstruct_strings(strings)
    print(f"Strings após reconstrução: {len(reconstructed)}")
    
    # Analisa o tipo de conteúdo
    print("\n=== ANALISANDO CONTEÚDO ===")
    analysis = analyze_content_type(reconstructed)
    
    # Gera resumo legível
    summary = generate_readable_summary(reconstructed, analysis)
    
    # Salva o resumo
    with open('decoded_summary.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    # Salva strings reconstruídas
    with open('reconstructed_strings.json', 'w', encoding='utf-8') as f:
        json.dump({
            'analysis': analysis,
            'reconstructed_strings': reconstructed,
            'total_count': len(reconstructed)
        }, f, indent=2, ensure_ascii=False)
    
    print(summary)
    
    print(f"\n=== ARQUIVOS GERADOS ===")
    print("- decoded_summary.txt: Resumo legível do conteúdo")
    print("- reconstructed_strings.json: Strings reconstruídas com análise")
    
    # Conclusão
    print(f"\n=== CONCLUSÃO ===")
    if analysis['framework'] == 'Material-UI (React)':
        print("✅ O arquivo contém código de uma aplicação web React com Material-UI")
        print("✅ Parece ser código legítimo de interface de usuário")
    else:
        print("⚠️  Tipo de código não identificado claramente")
    
    if analysis['suspicious']:
        print("⚠️  ATENÇÃO: Foram detectados padrões potencialmente suspeitos")
        print("   Recomenda-se análise manual adicional")
    else:
        print("✅ Nenhum padrão suspeito detectado")

if __name__ == "__main__":
    main()
