#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder para JavaScript ofuscado
Analisa e tenta decodificar o arquivo 4.js
"""

import re
import json
from typing import List, Dict, Any

def read_obfuscated_file(filename: str) -> str:
    """Lê o arquivo JavaScript ofuscado"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Erro ao ler arquivo: {e}")
        return ""

def analyze_obfuscation_patterns(code: str) -> Dict[str, Any]:
    """Analisa padrões de ofuscação no código"""
    patterns = {
        'hex_numbers': re.findall(r'0x[0-9a-fA-F]+', code),
        'function_names': re.findall(r'_0x[0-9a-fA-F]+', code),
        'string_literals': re.findall(r'[\'"`][^\'"`]*[\'"`]', code),
        'numeric_operations': re.findall(r'[-+*/]\s*0x[0-9a-fA-F]+', code),
        'array_access': re.findall(r'\[0x[0-9a-fA-F]+\]', code),
    }
    
    return patterns

def extract_string_array(code: str) -> List[str]:
    """Tenta extrair arrays de strings do código"""
    # Procura por arrays que podem conter strings codificadas
    array_pattern = r'\[([\'"][^\'"]*[\'"](?:\s*,\s*[\'"][^\'"]*[\'"])*)\]'
    arrays = re.findall(array_pattern, code)
    
    all_strings = []
    for array in arrays:
        # Extrai strings individuais do array
        strings = re.findall(r'[\'"]([^\'"]*)[\'"]', array)
        all_strings.extend(strings)
    
    return all_strings

def decode_hex_values(code: str) -> Dict[str, int]:
    """Decodifica valores hexadecimais encontrados"""
    hex_values = re.findall(r'0x[0-9a-fA-F]+', code)
    decoded = {}
    
    for hex_val in set(hex_values):
        try:
            decoded[hex_val] = int(hex_val, 16)
        except ValueError:
            continue
    
    return decoded

def find_decode_functions(code: str) -> List[Dict[str, str]]:
    """Encontra funções que podem ser usadas para decodificação"""
    # Padrão para funções que retornam algo baseado em parâmetros
    function_pattern = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]*\)\s*\{[^}]*return[^}]*\}'
    functions = re.findall(function_pattern, code, re.DOTALL)
    
    result = []
    for func_name in functions:
        # Encontra a função completa
        full_pattern = rf'function\s+{re.escape(func_name)}\s*\([^)]*\)\s*\{{[^}}]*\}}'
        match = re.search(full_pattern, code, re.DOTALL)
        if match:
            result.append({
                'name': func_name,
                'body': match.group(0)
            })
    
    return result

def extract_main_logic(code: str) -> str:
    """Tenta extrair a lógica principal do código"""
    # Remove funções de ofuscação e tenta encontrar o código principal
    lines = code.split('\n')
    main_logic = []
    
    for line in lines:
        # Pula linhas que são claramente parte da ofuscação
        if not any(pattern in line for pattern in ['_0x', 'function(', 'parseInt(', '0x']):
            if line.strip() and not line.strip().startswith('//'):
                main_logic.append(line)
    
    return '\n'.join(main_logic)

def analyze_string_operations(code: str) -> List[str]:
    """Analisa operações com strings que podem revelar o conteúdo"""
    operations = []
    
    # Procura por concatenações de strings
    concat_pattern = r'[\'"][^\'"]*[\'"] \+ [\'"][^\'"]*[\'"]'
    concatenations = re.findall(concat_pattern, code)
    operations.extend(concatenations)
    
    # Procura por operações String.fromCharCode
    charcode_pattern = r'String\.fromCharCode\([^)]+\)'
    charcodes = re.findall(charcode_pattern, code)
    operations.extend(charcodes)
    
    return operations

def main():
    print("=== DECODER PARA JAVASCRIPT OFUSCADO ===\n")
    
    # Lê o arquivo ofuscado
    code = read_obfuscated_file('4.js')
    if not code:
        return
    
    print(f"Arquivo carregado: {len(code)} caracteres")
    print(f"Número de linhas: {len(code.splitlines())}")
    
    # Analisa padrões de ofuscação
    print("\n=== ANÁLISE DE PADRÕES ===")
    patterns = analyze_obfuscation_patterns(code)
    
    for pattern_name, matches in patterns.items():
        if matches:
            print(f"{pattern_name}: {len(set(matches))} únicos de {len(matches)} total")
            if len(set(matches)) <= 10:
                print(f"  Exemplos: {list(set(matches))[:5]}")
    
    # Decodifica valores hexadecimais
    print("\n=== VALORES HEXADECIMAIS ===")
    hex_decoded = decode_hex_values(code)
    if hex_decoded:
        print(f"Encontrados {len(hex_decoded)} valores hex únicos")
        for hex_val, dec_val in list(hex_decoded.items())[:10]:
            print(f"  {hex_val} = {dec_val}")
        if len(hex_decoded) > 10:
            print(f"  ... e mais {len(hex_decoded) - 10}")
    
    # Extrai arrays de strings
    print("\n=== ARRAYS DE STRINGS ===")
    string_arrays = extract_string_array(code)
    if string_arrays:
        print(f"Encontradas {len(string_arrays)} strings em arrays")
        for i, string in enumerate(string_arrays[:10]):
            print(f"  {i+1}: '{string}'")
        if len(string_arrays) > 10:
            print(f"  ... e mais {len(string_arrays) - 10}")
    
    # Encontra funções de decodificação
    print("\n=== FUNÇÕES DE DECODIFICAÇÃO ===")
    decode_funcs = find_decode_functions(code)
    if decode_funcs:
        print(f"Encontradas {len(decode_funcs)} possíveis funções de decodificação")
        for i, func in enumerate(decode_funcs[:3]):
            print(f"  {i+1}: {func['name']}")
            print(f"     {func['body'][:100]}...")
    
    # Analisa operações com strings
    print("\n=== OPERAÇÕES COM STRINGS ===")
    string_ops = analyze_string_operations(code)
    if string_ops:
        print(f"Encontradas {len(string_ops)} operações com strings")
        for i, op in enumerate(string_ops[:5]):
            print(f"  {i+1}: {op}")
    
    # Tenta extrair lógica principal
    print("\n=== TENTATIVA DE EXTRAÇÃO DA LÓGICA PRINCIPAL ===")
    main_logic = extract_main_logic(code)
    if main_logic.strip():
        print("Possível lógica principal encontrada:")
        print(main_logic[:500])
        if len(main_logic) > 500:
            print("...")
    else:
        print("Não foi possível extrair lógica principal clara")
    
    # Salva análise em arquivo
    analysis_result = {
        'file_size': len(code),
        'patterns': {k: len(set(v)) for k, v in patterns.items()},
        'hex_values': len(hex_decoded),
        'string_arrays': len(string_arrays),
        'decode_functions': len(decode_funcs),
        'string_operations': len(string_ops)
    }
    
    with open('analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== ANÁLISE SALVA ===")
    print("Resultado da análise salvo em: analysis_result.json")

if __name__ == "__main__":
    main()
