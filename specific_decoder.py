#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Específico - Baseado na análise da estrutura real do código
"""

import re
import json

def read_file(filename: str) -> str:
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_main_function_and_array(code: str):
    """Extrai a função principal que contém o array de strings"""
    # Procura pela função _0x3e50 que parece ser a principal
    pattern = r'function\s+_0x3e50\s*\([^)]*\)\s*\{([^}]*)\}'
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        func_body = match.group(1)
        # Procura pelo array dentro da função
        array_pattern = r'const\s+\w+\s*=\s*\[(.*?)\];'
        array_match = re.search(array_pattern, func_body, re.DOTALL)
        
        if array_match:
            array_content = array_match.group(1)
            # Extrai strings do array
            strings = re.findall(r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]', array_content)
            return [s.encode().decode('unicode_escape') for s in strings]
    
    return []

def find_decoder_function_pattern(code: str):
    """Encontra o padrão da função de decodificação principal"""
    # Procura por _0x381c que parece ser a função de decodificação
    pattern = r'function\s+_0x381c\s*\([^)]*\)\s*\{([^}]*)\}'
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        return '_0x381c', match.group(1)
    
    return None, None

def extract_all_function_calls(code: str, func_name: str):
    """Extrai todas as chamadas para a função de decodificação"""
    # Padrão mais específico para as chamadas
    pattern = rf'{re.escape(func_name)}\s*\(\s*([^)]+)\s*\)'
    calls = re.findall(pattern, code)
    
    return calls

def simulate_decoder_logic(strings_array, calls, code):
    """Simula a lógica de decodificação baseada nos padrões encontrados"""
    mapping = {}
    
    # Analisa algumas chamadas para entender o padrão
    for call in calls[:50]:  # Limita para análise
        try:
            # Remove espaços e analisa a expressão
            call_clean = call.strip()
            
            # Se contém operações matemáticas
            if any(op in call_clean for op in ['+', '-', '*']):
                # Tenta avaliar expressões simples
                # Substitui 0x por valores decimais
                hex_pattern = r'0x[a-fA-F0-9]+'
                hex_matches = re.findall(hex_pattern, call_clean)
                
                eval_expr = call_clean
                for hex_val in hex_matches:
                    eval_expr = eval_expr.replace(hex_val, str(int(hex_val, 16)))
                
                try:
                    index = eval(eval_expr)
                    if 0 <= index < len(strings_array):
                        original_call = f"_0x381c({call})"
                        mapping[original_call] = strings_array[index]
                except:
                    continue
            else:
                # Chamada simples
                if call_clean.startswith('0x'):
                    index = int(call_clean, 16)
                    if 0 <= index < len(strings_array):
                        original_call = f"_0x381c({call})"
                        mapping[original_call] = strings_array[index]
        except:
            continue
    
    return mapping

def apply_decoding(code: str, mapping: dict) -> str:
    """Aplica a decodificação substituindo as chamadas pelas strings"""
    decoded = code
    
    # Ordena por tamanho para evitar substituições parciais
    sorted_mapping = sorted(mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    for call, string_val in sorted_mapping:
        # Escapa a string para JSON
        escaped_string = json.dumps(string_val, ensure_ascii=False)
        
        # Substitui a chamada
        pattern = re.escape(call)
        decoded = re.sub(pattern, escaped_string, decoded)
    
    return decoded

def clean_and_format(code: str) -> str:
    """Limpa e formata o código decodificado"""
    # Remove funções de ofuscação
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+[^}]*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove a função auto-executável principal
    code = re.sub(r'\(function\([^)]*\)[^}]*\{[^}]*while[^}]*\}[^}]*\}\)\([^)]*\);', '', code, flags=re.DOTALL)
    
    # Remove const _0x34f473 = (function(){...
    code = re.sub(r'const\s+_0x[a-fA-F0-9]+\s*=\s*\(function\(\)[^}]*\{[^}]*\}\)\(\);', '', code, flags=re.DOTALL)
    
    # Formata básico
    code = re.sub(r';\s*', ';\n', code)
    code = re.sub(r'\{\s*', '{\n  ', code)
    code = re.sub(r'\s*\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = [line.strip() for line in code.split('\n') if line.strip()]
    
    return '\n'.join(lines)

def main():
    print("=== DECODER ESPECÍFICO PARA O PADRÃO DETECTADO ===\n")
    
    # Carrega o código
    code = read_file('4.js')
    print(f"📂 Código carregado: {len(code):,} caracteres")
    
    # Extrai o array principal
    print("\n🔍 Extraindo array principal...")
    strings_array = extract_main_function_and_array(code)
    
    if not strings_array:
        print("❌ Não foi possível extrair o array principal")
        # Fallback: usa o array já extraído
        try:
            with open('decoded_strings.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                strings_array = data['meaningful_strings']
            print(f"✅ Usando array do arquivo anterior: {len(strings_array)} strings")
        except:
            print("❌ Não foi possível carregar strings anteriores")
            return
    else:
        print(f"✅ Array extraído: {len(strings_array)} strings")
    
    # Encontra a função de decodificação
    print("\n🔧 Analisando função de decodificação...")
    func_name, func_body = find_decoder_function_pattern(code)
    
    if func_name:
        print(f"✅ Função encontrada: {func_name}")
    else:
        func_name = "_0x381c"  # Usa o padrão detectado
        print(f"⚠️  Usando função padrão: {func_name}")
    
    # Extrai todas as chamadas
    print("\n🗺️  Extraindo chamadas de função...")
    calls = extract_all_function_calls(code, func_name)
    print(f"✅ Encontradas {len(calls)} chamadas")
    
    # Simula a decodificação
    print("\n🔓 Simulando lógica de decodificação...")
    mapping = simulate_decoder_logic(strings_array, calls, code)
    print(f"✅ Mapeadas {len(mapping)} chamadas")
    
    if mapping:
        # Aplica a decodificação
        print("\n🔄 Aplicando decodificação...")
        decoded_code = apply_decoding(code, mapping)
        
        # Limpa e formata
        print("🧹 Limpando código...")
        final_code = clean_and_format(decoded_code)
        
        # Salva o resultado
        with open('codigo_final_decodificado.js', 'w', encoding='utf-8') as f:
            f.write(final_code)
        
        print("✅ Código salvo em: codigo_final_decodificado.js")
        
        # Mostra prévia
        print(f"\n📄 PRÉVIA DO CÓDIGO DECODIFICADO:")
        print("=" * 80)
        preview = final_code[:3000]
        print(preview)
        if len(final_code) > 3000:
            print(f"\n... (continua - total: {len(final_code):,} caracteres)")
        print("=" * 80)
        
        # Estatísticas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Código original: {len(code):,} caracteres")
        print(f"   • Código decodificado: {len(final_code):,} caracteres")
        print(f"   • Strings no array: {len(strings_array)}")
        print(f"   • Chamadas mapeadas: {len(mapping)}")
        print(f"   • Taxa de decodificação: {len(mapping)/len(calls)*100:.1f}%")
        
    else:
        print("❌ Não foi possível criar mapeamento de decodificação")
        
        # Tenta uma abordagem mais simples
        print("\n🔄 Tentando abordagem alternativa...")
        
        # Substitui algumas strings conhecidas diretamente
        simple_mapping = {}
        for i, string in enumerate(strings_array[:100]):
            if len(string) > 3:
                hex_val = hex(i)
                call_pattern = f"_0x381c({hex_val})"
                simple_mapping[call_pattern] = string
        
        if simple_mapping:
            decoded_simple = apply_decoding(code, simple_mapping)
            final_simple = clean_and_format(decoded_simple)
            
            with open('codigo_parcial_decodificado.js', 'w', encoding='utf-8') as f:
                f.write(final_simple)
            
            print(f"✅ Decodificação parcial salva em: codigo_parcial_decodificado.js")
            print(f"📊 {len(simple_mapping)} substituições realizadas")

if __name__ == "__main__":
    main()
