#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Completo - Reconstrói o código JavaScript original
"""

import re
import json
from typing import List, Dict, Tu<PERSON>

def read_file(filename: str) -> str:
    """Lê o arquivo JavaScript ofuscado"""
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_string_array_complete(code: str) -> List[str]:
    """Extrai o array completo de strings do código"""
    # Procura pelo padrão da função que contém o array principal
    # Geralmente está em uma função auto-executável
    pattern = r'function\s+_0x[a-fA-F0-9]+\s*\(\s*\)\s*\{\s*(?:const|var)\s+\w+\s*=\s*\[(.*?)\];'
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        array_content = match.group(1)
        # Extrai todas as strings, incluindo as com escape
        strings = []
        # Padrão mais robusto para capturar strings com escapes
        string_pattern = r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]'
        matches = re.findall(string_pattern, array_content)
        
        for match in matches:
            # Decodifica escapes
            decoded = match.encode().decode('unicode_escape')
            strings.append(decoded)
        
        return strings
    
    # Fallback: procura por arrays grandes
    large_array_pattern = r'\[([\'"][^\'"]*[\'"](?:\s*,\s*[\'"][^\'"]*[\'"]){200,})\]'
    match = re.search(large_array_pattern, code, re.DOTALL)
    if match:
        array_content = match.group(1)
        strings = []
        string_pattern = r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]'
        matches = re.findall(string_pattern, array_content)
        
        for match in matches:
            try:
                decoded = match.encode().decode('unicode_escape')
                strings.append(decoded)
            except:
                strings.append(match)
        
        return strings
    
    return []

def find_decoder_function_name(code: str) -> str:
    """Encontra o nome da função principal de decodificação"""
    # Procura por função que retorna algo do array
    pattern = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]+\)\s*\{\s*return\s+[^}]*\[[^}]*\]\s*;?\s*\}'
    matches = re.findall(pattern, code)
    
    if matches:
        return matches[0]
    
    # Fallback: procura por qualquer função _0x que usa arrays
    pattern2 = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]+\)\s*\{[^}]*return[^}]*_0x[a-fA-F0-9]+[^}]*\}'
    matches2 = re.findall(pattern2, code)
    
    if matches2:
        return matches2[0]
    
    return ""

def create_string_mapping(strings: List[str], code: str, decoder_func: str) -> Dict[str, str]:
    """Cria mapeamento de chamadas de função para strings"""
    mapping = {}
    
    if not decoder_func:
        return mapping
    
    # Procura por todas as chamadas da função de decodificação
    # Padrão: _0xfuncname(0xnumber) ou _0xfuncname(0xnumber-0xnumber) etc
    pattern = rf'{re.escape(decoder_func)}\s*\(\s*(0x[a-fA-F0-9]+(?:\s*[-+]\s*0x[a-fA-F0-9]+)*)\s*\)'
    calls = re.findall(pattern, code)
    
    for call in calls:
        try:
            # Avalia a expressão matemática
            # Remove espaços e substitui 0x por int
            expr = call.replace(' ', '')
            
            # Se é só um número hex
            if re.match(r'^0x[a-fA-F0-9]+$', expr):
                index = int(expr, 16)
            else:
                # Se tem operações matemáticas
                # Substitui 0x por int equivalente
                hex_nums = re.findall(r'0x[a-fA-F0-9]+', expr)
                eval_expr = expr
                for hex_num in hex_nums:
                    eval_expr = eval_expr.replace(hex_num, str(int(hex_num, 16)))
                
                # Avalia a expressão
                index = eval(eval_expr)
            
            # Verifica se o índice é válido
            if 0 <= index < len(strings):
                full_call = f"{decoder_func}({call})"
                mapping[full_call] = strings[index]
                
        except (ValueError, SyntaxError, IndexError):
            continue
    
    return mapping

def decode_javascript_code(code: str, string_mapping: Dict[str, str]) -> str:
    """Substitui as chamadas de função pelas strings reais"""
    decoded_code = code
    
    # Ordena por tamanho decrescente para evitar substituições parciais
    sorted_calls = sorted(string_mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    for call, string_value in sorted_calls:
        # Escapa caracteres especiais na string de substituição
        escaped_string = json.dumps(string_value)
        
        # Substitui a chamada pela string
        # Usa re.escape para tratar caracteres especiais na chamada
        pattern = re.escape(call)
        decoded_code = re.sub(pattern, escaped_string, decoded_code)
    
    return decoded_code

def clean_decoded_code(code: str) -> str:
    """Limpa e formata o código decodificado"""
    # Remove funções de ofuscação desnecessárias
    # Remove a função do array de strings
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+\s*\(\s*\)\s*\{[^}]*const\s+\w+\s*=\s*\[[^\]]*\];[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove funções de decodificação
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+\s*\([^)]*\)\s*\{[^}]*return[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove a função auto-executável de embaralhamento
    code = re.sub(r'\(function\s*\([^)]*\)\s*\{[^}]*while\s*\([^}]*\}[^}]*\}\s*\)\s*\([^)]*\);', '', code, flags=re.DOTALL)
    
    # Remove linhas vazias excessivas
    code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)
    
    # Remove espaços no início e fim
    code = code.strip()
    
    return code

def format_javascript(code: str) -> str:
    """Formata o JavaScript para melhor legibilidade"""
    # Adiciona quebras de linha após ; quando apropriado
    code = re.sub(r';(?!\s*[}\]\)])', ';\n', code)
    
    # Adiciona quebras de linha após {
    code = re.sub(r'\{(?!\s*\})', '{\n', code)
    
    # Adiciona quebras de linha antes de }
    code = re.sub(r'(?<!\{\s*)\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = code.split('\n')
    formatted_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line == '':
            if not prev_empty:
                formatted_lines.append('')
            prev_empty = True
        else:
            formatted_lines.append(line)
            prev_empty = False
    
    return '\n'.join(formatted_lines)

def main():
    print("=== DECODER COMPLETO - RECONSTRUÇÃO DO CÓDIGO ORIGINAL ===\n")
    
    # Lê o arquivo ofuscado
    print("📂 Carregando arquivo ofuscado...")
    code = read_file('4.js')
    print(f"✅ Arquivo carregado: {len(code):,} caracteres")
    
    # Extrai o array de strings
    print("\n🔍 Extraindo array de strings...")
    strings = extract_string_array_complete(code)
    print(f"✅ Extraídas {len(strings)} strings")
    
    if not strings:
        print("❌ Não foi possível extrair o array de strings")
        return
    
    # Encontra a função de decodificação
    print("\n🔧 Identificando função de decodificação...")
    decoder_func = find_decoder_function_name(code)
    if decoder_func:
        print(f"✅ Função encontrada: {decoder_func}")
    else:
        print("❌ Função de decodificação não encontrada")
        return
    
    # Cria mapeamento de strings
    print("\n🗺️  Criando mapeamento de strings...")
    string_mapping = create_string_mapping(strings, code, decoder_func)
    print(f"✅ Mapeadas {len(string_mapping)} chamadas de função")
    
    if not string_mapping:
        print("❌ Não foi possível criar mapeamento")
        return
    
    # Decodifica o código
    print("\n🔓 Decodificando código JavaScript...")
    decoded_code = decode_javascript_code(code, string_mapping)
    
    # Limpa o código
    print("🧹 Limpando código decodificado...")
    cleaned_code = clean_decoded_code(decoded_code)
    
    # Formata o código
    print("✨ Formatando código...")
    formatted_code = format_javascript(cleaned_code)
    
    # Salva o resultado
    print("\n💾 Salvando código decodificado...")
    with open('codigo_decodificado.js', 'w', encoding='utf-8') as f:
        f.write(formatted_code)
    
    print("✅ Código salvo em: codigo_decodificado.js")
    
    # Mostra uma prévia
    print(f"\n📄 PRÉVIA DO CÓDIGO DECODIFICADO:")
    print("=" * 60)
    preview = formatted_code[:2000]
    print(preview)
    if len(formatted_code) > 2000:
        print(f"\n... (código continua - total: {len(formatted_code):,} caracteres)")
    print("=" * 60)
    
    print(f"\n🎉 DECODE CONCLUÍDO!")
    print(f"📊 Estatísticas:")
    print(f"   • Código original: {len(code):,} caracteres")
    print(f"   • Código decodificado: {len(formatted_code):,} caracteres")
    print(f"   • Strings substituídas: {len(string_mapping)}")
    print(f"   • Arquivo salvo: codigo_decodificado.js")

if __name__ == "__main__":
    main()
