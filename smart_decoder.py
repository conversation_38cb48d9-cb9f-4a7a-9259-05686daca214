#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Inteligente - Analisa a estrutura específica e decodifica tudo
"""

import re
import json
from typing import List, Dict, <PERSON><PERSON>

def read_file(filename: str) -> str:
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_main_string_array(code: str) -> List[str]:
    """Extrai o array principal de strings da função _0x3e50"""
    # Procura especificamente pela função _0x3e50 que contém o array
    pattern = r'function\s+_0x3e50\s*\(\s*\)\s*\{\s*const\s+\w+\s*=\s*\[(.*?)\];'
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        array_content = match.group(1)
        # Extrai todas as strings do array
        strings = []
        # Padrão mais robusto para strings com escapes
        string_pattern = r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]'
        matches = re.findall(string_pattern, array_content)
        
        for s in matches:
            try:
                # Decod<PERSON><PERSON> escapes unicode e hex
                decoded = s.encode('utf-8').decode('unicode_escape')
                strings.append(decoded)
            except:
                strings.append(s)
        
        return strings
    
    return []

def analyze_decoder_function(code: str) -> Tuple[str, str]:
    """Analisa a função de decodificação _0x381c"""
    pattern = r'function\s+_0x381c\s*\([^)]*\)\s*\{([^}]*)\}'
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        func_body = match.group(1)
        return '_0x381c', func_body
    
    return None, None

def extract_all_calls_with_context(code: str) -> List[Tuple[str, str, int]]:
    """Extrai todas as chamadas _0x381c com contexto"""
    calls = []
    pattern = r'_0x381c\s*\(\s*([^)]+)\s*\)'
    
    for match in re.finditer(pattern, code):
        call_content = match.group(1)
        start_pos = match.start()
        
        # Pega contexto ao redor da chamada
        context_start = max(0, start_pos - 50)
        context_end = min(len(code), start_pos + 100)
        context = code[context_start:context_end]
        
        calls.append((match.group(0), call_content, start_pos))
    
    return calls

def smart_decode_expression(expr: str, strings_array: List[str]) -> str:
    """Decodificação inteligente de expressões"""
    try:
        expr_clean = expr.strip().replace(' ', '')
        
        # Se é uma expressão matemática complexa
        if any(op in expr_clean for op in ['+', '-', '*', '/', '%']):
            # Substitui todos os hex por decimais
            eval_expr = expr_clean
            hex_nums = re.findall(r'0x[a-fA-F0-9]+', expr_clean)
            
            for hex_num in hex_nums:
                decimal_val = int(hex_num, 16)
                eval_expr = eval_expr.replace(hex_num, str(decimal_val))
            
            # Avalia a expressão
            try:
                index = eval(eval_expr)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
        
        # Se é um hex simples
        elif expr_clean.startswith('0x'):
            try:
                index = int(expr_clean, 16)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
        
        # Se é decimal
        elif expr_clean.isdigit():
            try:
                index = int(expr_clean)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
        
        # Se tem múltiplos parâmetros, tenta o primeiro
        elif ',' in expr_clean:
            first_param = expr_clean.split(',')[0].strip()
            return smart_decode_expression(first_param, strings_array)
    
    except Exception as e:
        pass
    
    return None

def create_comprehensive_mapping(code: str, strings_array: List[str]) -> Dict[str, str]:
    """Cria mapeamento abrangente de todas as chamadas"""
    mapping = {}
    
    # Extrai todas as chamadas com contexto
    calls = extract_all_calls_with_context(code)
    print(f"🔍 Analisando {len(calls)} chamadas...")
    
    successful_decodes = 0
    
    for full_call, expr, pos in calls:
        decoded_string = smart_decode_expression(expr, strings_array)
        
        if decoded_string is not None:
            mapping[full_call] = decoded_string
            successful_decodes += 1
    
    print(f"✅ Decodificadas {successful_decodes} de {len(calls)} chamadas")
    return mapping

def decode_hex_and_unicode(code: str) -> str:
    """Decodifica todas as strings hex e unicode"""
    # Decodifica \\x
    def hex_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\x([a-fA-F0-9]{2})', hex_replacer, code)
    
    # Decodifica \\u
    def unicode_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\u([a-fA-F0-9]{4})', unicode_replacer, code)
    
    return code

def apply_smart_substitution(code: str, mapping: Dict[str, str]) -> str:
    """Aplica substituições inteligentes"""
    decoded = code
    substitutions = 0
    
    # Ordena por tamanho para evitar substituições parciais
    sorted_mapping = sorted(mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    for call, string_val in sorted_mapping:
        # Escapa a string adequadamente
        try:
            if '"' in string_val and "'" not in string_val:
                escaped_string = f"'{string_val}'"
            elif "'" in string_val and '"' not in string_val:
                escaped_string = f'"{string_val}"'
            else:
                escaped_string = json.dumps(string_val, ensure_ascii=False)
        except:
            escaped_string = f'"{string_val}"'
        
        # Substitui usando regex para ser mais preciso
        pattern = re.escape(call)
        old_decoded = decoded
        decoded = re.sub(pattern, escaped_string, decoded)
        
        if decoded != old_decoded:
            substitutions += 1
    
    print(f"🔄 Realizadas {substitutions} substituições")
    
    # Decodifica hex e unicode
    decoded = decode_hex_and_unicode(decoded)
    
    return decoded

def clean_obfuscation_artifacts(code: str) -> str:
    """Remove artefatos de ofuscação"""
    # Remove a função _0x3e50 que contém o array
    code = re.sub(r'function\s+_0x3e50\s*\(\s*\)\s*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove a função _0x381c de decodificação
    code = re.sub(r'function\s+_0x381c\s*\([^)]*\)\s*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove a função auto-executável de embaralhamento
    code = re.sub(r'\(function\s*\([^)]*\)\s*\{[^}]*while[^}]*\}[^}]*\}\s*\)\s*\([^)]*\);', '', code, flags=re.DOTALL)
    
    # Remove declarações vazias
    code = re.sub(r'(?:const|var|let)\s+_0x[a-fA-F0-9]+\s*=\s*_0x3e50\s*\(\s*\);', '', code)
    
    return code

def format_javascript_code(code: str) -> str:
    """Formata o código JavaScript"""
    # Adiciona quebras de linha apropriadas
    code = re.sub(r';\s*(?![}\]\)])', ';\n', code)
    code = re.sub(r'\{\s*(?!\})', '{\n  ', code)
    code = re.sub(r'(?<!\{\s*)\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = code.split('\n')
    formatted_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line == '':
            if not prev_empty:
                formatted_lines.append('')
            prev_empty = True
        else:
            formatted_lines.append(line)
            prev_empty = False
    
    return '\n'.join(formatted_lines)

def main():
    print("=== DECODER INTELIGENTE - DECODE COMPLETO ===\n")
    
    # Carrega o código
    code = read_file('4.js')
    print(f"📂 Código carregado: {len(code):,} caracteres")
    
    # Extrai o array principal de strings
    print("\n🔍 Extraindo array principal de strings...")
    strings_array = extract_main_string_array(code)
    
    if not strings_array:
        print("❌ Não foi possível extrair o array principal!")
        return
    
    print(f"✅ Extraídas {len(strings_array)} strings do array principal")
    
    # Analisa a função de decodificação
    print("\n🔧 Analisando função de decodificação...")
    func_name, func_body = analyze_decoder_function(code)
    
    if func_name:
        print(f"✅ Função encontrada: {func_name}")
    else:
        print("⚠️  Função de decodificação não encontrada, continuando...")
    
    # Cria mapeamento abrangente
    print("\n🗺️  Criando mapeamento abrangente...")
    mapping = create_comprehensive_mapping(code, strings_array)
    
    if not mapping:
        print("❌ Não foi possível criar mapeamento!")
        return
    
    print(f"✅ Mapeamento criado com {len(mapping)} entradas")
    
    # Aplica substituições inteligentes
    print("\n🔄 Aplicando substituições inteligentes...")
    decoded_code = apply_smart_substitution(code, mapping)
    
    # Remove artefatos de ofuscação
    print("\n🧹 Removendo artefatos de ofuscação...")
    cleaned_code = clean_obfuscation_artifacts(decoded_code)
    
    # Formata o código
    print("\n✨ Formatando código...")
    final_code = format_javascript_code(cleaned_code)
    
    # Salva o resultado
    print("\n💾 Salvando código completamente decodificado...")
    with open('codigo_final_decodificado.js', 'w', encoding='utf-8') as f:
        f.write(final_code)
    
    print("✅ Código salvo em: codigo_final_decodificado.js")
    
    # Estatísticas
    print(f"\n📊 ESTATÍSTICAS:")
    print(f"   • Código original: {len(code):,} caracteres")
    print(f"   • Código decodificado: {len(final_code):,} caracteres")
    print(f"   • Strings no array: {len(strings_array)}")
    print(f"   • Substituições realizadas: {len(mapping)}")
    print(f"   • Redução de tamanho: {((len(code) - len(final_code)) / len(code) * 100):.1f}%")
    
    # Mostra prévia
    print(f"\n📄 PRÉVIA DO CÓDIGO COMPLETAMENTE DECODIFICADO:")
    print("=" * 80)
    preview = final_code[:5000]
    print(preview)
    if len(final_code) > 5000:
        print(f"\n... (continua - total: {len(final_code):,} caracteres)")
    print("=" * 80)
    
    print(f"\n🎉 DECODE COMPLETO FINALIZADO COM SUCESSO!")

if __name__ == "__main__":
    main()
