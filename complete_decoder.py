#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Completo - Decodifica TUDO do JavaScript ofuscado
"""

import re
import json
import ast
from typing import List, Dict, Tuple, Set

def read_file(filename: str) -> str:
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_all_string_arrays(code: str) -> List[str]:
    """Extrai TODOS os arrays de strings do código"""
    all_strings = []
    
    # Padrão 1: Arrays grandes em funções
    pattern1 = r'(?:const|var|let)\s+\w+\s*=\s*\[((?:[\'"][^\'\"]*[\'"](?:\s*,\s*)?)+)\]'
    matches1 = re.findall(pattern1, code, re.DOTALL)
    
    for match in matches1:
        strings = re.findall(r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]', match)
        for s in strings:
            try:
                decoded = s.encode().decode('unicode_escape')
                all_strings.append(decoded)
            except:
                all_strings.append(s)
    
    # Padrão 2: Arrays inline
    pattern2 = r'\[([\'"][^\'\"]*[\'"](?:\s*,\s*[\'"][^\'\"]*[\'"]){10,})\]'
    matches2 = re.findall(pattern2, code, re.DOTALL)
    
    for match in matches2:
        strings = re.findall(r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]', match)
        for s in strings:
            try:
                decoded = s.encode().decode('unicode_escape')
                all_strings.append(decoded)
            except:
                all_strings.append(s)
    
    # Remove duplicatas mantendo ordem
    seen = set()
    unique_strings = []
    for s in all_strings:
        if s not in seen:
            seen.add(s)
            unique_strings.append(s)
    
    return unique_strings

def find_all_decoder_functions(code: str) -> List[str]:
    """Encontra TODAS as funções de decodificação"""
    functions = []
    
    # Padrão para funções _0x que retornam algo de array
    pattern = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]*\)\s*\{[^}]*return[^}]*\[[^}]*\][^}]*\}'
    matches = re.findall(pattern, code)
    functions.extend(matches)
    
    # Padrão para funções que fazem operações matemáticas
    pattern2 = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]*\)\s*\{[^}]*[-+*/][^}]*return[^}]*\}'
    matches2 = re.findall(pattern2, code)
    functions.extend(matches2)
    
    # Remove duplicatas
    return list(set(functions))

def extract_all_function_calls(code: str, functions: List[str]) -> Dict[str, List[str]]:
    """Extrai TODAS as chamadas para as funções de decodificação"""
    all_calls = {}
    
    for func in functions:
        pattern = rf'{re.escape(func)}\s*\(\s*([^)]+)\s*\)'
        calls = re.findall(pattern, code)
        all_calls[func] = calls
    
    return all_calls

def advanced_decode_calls(strings_array: List[str], func_calls: Dict[str, List[str]]) -> Dict[str, str]:
    """Decodificação avançada de todas as chamadas"""
    mapping = {}
    
    for func_name, calls in func_calls.items():
        print(f"🔧 Processando função {func_name}: {len(calls)} chamadas")
        
        for call in calls:
            try:
                # Limpa a chamada
                call_clean = call.strip().replace(' ', '')
                
                # Tenta diferentes estratégias de decodificação
                index = None
                
                # Estratégia 1: Número hexadecimal simples
                if re.match(r'^0x[a-fA-F0-9]+$', call_clean):
                    index = int(call_clean, 16)
                
                # Estratégia 2: Expressão matemática com hex
                elif any(op in call_clean for op in ['+', '-', '*', '/', '%']):
                    # Substitui todos os hex por decimais
                    expr = call_clean
                    hex_nums = re.findall(r'0x[a-fA-F0-9]+', expr)
                    for hex_num in hex_nums:
                        expr = expr.replace(hex_num, str(int(hex_num, 16)))
                    
                    # Avalia a expressão
                    try:
                        index = eval(expr)
                    except:
                        continue
                
                # Estratégia 3: Múltiplos parâmetros
                elif ',' in call_clean:
                    params = [p.strip() for p in call_clean.split(',')]
                    # Tenta usar o primeiro parâmetro
                    if params[0].startswith('0x'):
                        index = int(params[0], 16)
                
                # Estratégia 4: Número decimal
                elif call_clean.isdigit():
                    index = int(call_clean)
                
                # Se conseguiu um índice válido
                if index is not None and 0 <= index < len(strings_array):
                    full_call = f"{func_name}({call})"
                    mapping[full_call] = strings_array[index]
                    
            except Exception as e:
                continue
    
    return mapping

def decode_hex_strings(code: str) -> str:
    """Decodifica strings hexadecimais inline"""
    # Padrão para strings \\x
    def hex_replacer(match):
        hex_str = match.group(1)
        try:
            return chr(int(hex_str, 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\x([a-fA-F0-9]{2})', hex_replacer, code)
    return code

def decode_unicode_strings(code: str) -> str:
    """Decodifica strings unicode"""
    def unicode_replacer(match):
        unicode_str = match.group(1)
        try:
            return chr(int(unicode_str, 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\u([a-fA-F0-9]{4})', unicode_replacer, code)
    return code

def apply_complete_decoding(code: str, mapping: Dict[str, str]) -> str:
    """Aplica decodificação completa"""
    decoded = code
    
    print(f"🔄 Aplicando {len(mapping)} substituições...")
    
    # Ordena por tamanho decrescente para evitar substituições parciais
    sorted_mapping = sorted(mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    substitutions = 0
    for call, string_val in sorted_mapping:
        # Escapa a string para JSON
        try:
            escaped_string = json.dumps(string_val, ensure_ascii=False)
        except:
            escaped_string = f'"{string_val}"'
        
        # Substitui a chamada
        pattern = re.escape(call)
        old_decoded = decoded
        decoded = re.sub(pattern, escaped_string, decoded)
        
        if decoded != old_decoded:
            substitutions += 1
    
    print(f"✅ Realizadas {substitutions} substituições")
    
    # Decodifica strings hex e unicode
    decoded = decode_hex_strings(decoded)
    decoded = decode_unicode_strings(decoded)
    
    return decoded

def clean_and_beautify(code: str) -> str:
    """Limpa e embeleza o código final"""
    # Remove funções de ofuscação
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+\s*\([^)]*\)\s*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove a função auto-executável de embaralhamento
    code = re.sub(r'\(function\s*\([^)]*\)\s*\{[^}]*while[^}]*\}[^}]*\}\s*\)\s*\([^)]*\);', '', code, flags=re.DOTALL)
    
    # Remove declarações de arrays vazios
    code = re.sub(r'(?:const|var|let)\s+_0x[a-fA-F0-9]+\s*=\s*\(function\(\)[^}]*\{[^}]*\}\)\(\);', '', code, flags=re.DOTALL)
    
    # Formata o código
    code = re.sub(r';\s*', ';\n', code)
    code = re.sub(r'\{\s*', '{\n  ', code)
    code = re.sub(r'\s*\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = code.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line == '':
            if not prev_empty:
                cleaned_lines.append('')
            prev_empty = True
        else:
            cleaned_lines.append(line)
            prev_empty = False
    
    return '\n'.join(cleaned_lines)

def main():
    print("=== DECODER COMPLETO - DECODIFICA TUDO! ===\n")
    
    # Carrega o código
    code = read_file('4.js')
    print(f"📂 Código carregado: {len(code):,} caracteres")
    
    # Extrai TODOS os arrays de strings
    print("\n🔍 Extraindo TODOS os arrays de strings...")
    all_strings = extract_all_string_arrays(code)
    print(f"✅ Extraídas {len(all_strings)} strings únicas")
    
    if not all_strings:
        print("❌ Nenhuma string encontrada!")
        return
    
    # Encontra TODAS as funções de decodificação
    print("\n🔧 Encontrando TODAS as funções de decodificação...")
    decoder_functions = find_all_decoder_functions(code)
    print(f"✅ Encontradas {len(decoder_functions)} funções: {decoder_functions}")
    
    if not decoder_functions:
        print("❌ Nenhuma função de decodificação encontrada!")
        return
    
    # Extrai TODAS as chamadas
    print("\n🗺️  Extraindo TODAS as chamadas de função...")
    all_calls = extract_all_function_calls(code, decoder_functions)
    total_calls = sum(len(calls) for calls in all_calls.values())
    print(f"✅ Encontradas {total_calls} chamadas totais")
    
    # Decodificação avançada
    print("\n🔓 Executando decodificação avançada...")
    mapping = advanced_decode_calls(all_strings, all_calls)
    print(f"✅ Mapeadas {len(mapping)} chamadas para strings")
    
    if not mapping:
        print("❌ Não foi possível criar mapeamento!")
        return
    
    # Aplica decodificação completa
    print("\n🔄 Aplicando decodificação completa...")
    decoded_code = apply_complete_decoding(code, mapping)
    
    # Limpa e embeleza
    print("\n🧹 Limpando e formatando código...")
    final_code = clean_and_beautify(decoded_code)
    
    # Salva o resultado
    print("\n💾 Salvando código completamente decodificado...")
    with open('codigo_completo_decodificado.js', 'w', encoding='utf-8') as f:
        f.write(final_code)
    
    print("✅ Código salvo em: codigo_completo_decodificado.js")
    
    # Estatísticas finais
    print(f"\n📊 ESTATÍSTICAS FINAIS:")
    print(f"   • Código original: {len(code):,} caracteres")
    print(f"   • Código decodificado: {len(final_code):,} caracteres")
    print(f"   • Strings extraídas: {len(all_strings)}")
    print(f"   • Funções de decode: {len(decoder_functions)}")
    print(f"   • Chamadas mapeadas: {len(mapping)}")
    print(f"   • Taxa de decodificação: {len(mapping)/total_calls*100:.1f}%")
    
    # Mostra prévia do código decodificado
    print(f"\n📄 PRÉVIA DO CÓDIGO COMPLETAMENTE DECODIFICADO:")
    print("=" * 80)
    preview = final_code[:4000]
    print(preview)
    if len(final_code) > 4000:
        print(f"\n... (continua - total: {len(final_code):,} caracteres)")
    print("=" * 80)
    
    print(f"\n🎉 DECODE COMPLETO FINALIZADO!")
    print(f"📁 Arquivo final: codigo_completo_decodificado.js")

if __name__ == "__main__":
    main()
