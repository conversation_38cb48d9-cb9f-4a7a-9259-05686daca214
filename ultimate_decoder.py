#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Decoder - Decodifica até 100% do JavaScript
Múltiplas passadas até não sobrar nada ofuscado
"""

import re
import json
from typing import List, Dict, Tuple, Set

def read_file(filename: str) -> str:
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_all_function_names(code: str) -> List[str]:
    """Extrai TODOS os nomes de funções _0x do código"""
    pattern = r'function\s+(_0x[a-fA-F0-9]+)'
    functions = re.findall(pattern, code)
    
    # Também procura por funções que são chamadas mas não definidas
    call_pattern = r'(_0x[a-fA-F0-9]+)\s*\('
    called_functions = re.findall(call_pattern, code)
    
    all_functions = list(set(functions + called_functions))
    return all_functions

def extract_all_string_arrays_advanced(code: str) -> List[str]:
    """Extração avançada de TODOS os arrays de strings"""
    all_strings = []
    
    # Padrão 1: Arrays em funções auto-executáveis
    pattern1 = r'function\s*\(\s*\)\s*\{[^}]*(?:const|var|let)\s+\w+\s*=\s*\[(.*?)\];'
    matches1 = re.findall(pattern1, code, re.DOTALL)
    
    # Padrão 2: Arrays grandes inline
    pattern2 = r'\[((?:[\'"][^\'\"]*[\'"](?:\s*,\s*)?){50,})\]'
    matches2 = re.findall(pattern2, code, re.DOTALL)
    
    # Padrão 3: Arrays em declarações de variáveis
    pattern3 = r'(?:const|var|let)\s+\w+\s*=\s*\[((?:[\'"][^\'\"]*[\'"](?:\s*,\s*)?){20,})\]'
    matches3 = re.findall(pattern3, code, re.DOTALL)
    
    all_matches = matches1 + matches2 + matches3
    
    for match in all_matches:
        strings = re.findall(r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]', match)
        for s in strings:
            try:
                decoded = s.encode('utf-8').decode('unicode_escape')
                all_strings.append(decoded)
            except:
                all_strings.append(s)
    
    # Remove duplicatas mantendo ordem
    seen = set()
    unique_strings = []
    for s in all_strings:
        if s not in seen:
            seen.add(s)
            unique_strings.append(s)
    
    return unique_strings

def extract_all_calls_comprehensive(code: str, functions: List[str]) -> Dict[str, List[str]]:
    """Extração abrangente de TODAS as chamadas de função"""
    all_calls = {}
    
    for func in functions:
        # Padrões mais específicos para cada tipo de chamada
        patterns = [
            rf'{re.escape(func)}\s*\(\s*([^)]+)\s*\)',  # Padrão geral
            rf'{re.escape(func)}\s*\(\s*(0x[a-fA-F0-9]+(?:\s*[-+*/]\s*0x[a-fA-F0-9]+)*)\s*\)',  # Hex com operações
            rf'{re.escape(func)}\s*\(\s*(-?0x[a-fA-F0-9]+)\s*\)',  # Hex simples
            rf'{re.escape(func)}\s*\(\s*(\d+)\s*\)',  # Decimal simples
        ]
        
        calls = set()
        for pattern in patterns:
            matches = re.findall(pattern, code)
            calls.update(matches)
        
        all_calls[func] = list(calls)
    
    return all_calls

def advanced_expression_evaluator(expr: str, strings_array: List[str]) -> str:
    """Avaliador avançado de expressões matemáticas"""
    try:
        expr_clean = expr.strip().replace(' ', '')
        
        # Se contém vírgulas, tenta usar o primeiro parâmetro
        if ',' in expr_clean:
            params = [p.strip() for p in expr_clean.split(',')]
            for param in params:
                result = advanced_expression_evaluator(param, strings_array)
                if result is not None:
                    return result
            return None
        
        # Se é uma expressão matemática
        if any(op in expr_clean for op in ['+', '-', '*', '/', '%', '&', '|', '^', '<<', '>>']):
            # Substitui todos os hex por decimais
            eval_expr = expr_clean
            hex_nums = re.findall(r'-?0x[a-fA-F0-9]+', expr_clean)
            
            for hex_num in hex_nums:
                decimal_val = int(hex_num, 16)
                eval_expr = eval_expr.replace(hex_num, str(decimal_val))
            
            # Avalia a expressão
            try:
                index = eval(eval_expr)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
        
        # Se é um hex simples
        elif expr_clean.startswith('0x') or expr_clean.startswith('-0x'):
            try:
                index = int(expr_clean, 16)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
        
        # Se é decimal
        elif expr_clean.lstrip('-').isdigit():
            try:
                index = int(expr_clean)
                if 0 <= index < len(strings_array):
                    return strings_array[index]
            except:
                pass
    
    except Exception:
        pass
    
    return None

def create_ultimate_mapping(code: str, strings_array: List[str], functions: List[str]) -> Dict[str, str]:
    """Cria mapeamento ultimate de TODAS as chamadas possíveis"""
    mapping = {}
    
    # Extrai todas as chamadas
    all_calls = extract_all_calls_comprehensive(code, functions)
    
    total_calls = 0
    successful_decodes = 0
    
    for func_name, calls in all_calls.items():
        print(f"🔧 Processando {func_name}: {len(calls)} chamadas")
        total_calls += len(calls)
        
        for call in calls:
            decoded_string = advanced_expression_evaluator(call, strings_array)
            
            if decoded_string is not None:
                full_call = f"{func_name}({call})"
                mapping[full_call] = decoded_string
                successful_decodes += 1
    
    print(f"✅ Total: {successful_decodes}/{total_calls} chamadas decodificadas ({successful_decodes/total_calls*100:.1f}%)")
    return mapping

def apply_multiple_passes(code: str, mapping: Dict[str, str]) -> str:
    """Aplica múltiplas passadas de decodificação"""
    decoded = code
    total_substitutions = 0
    
    # Ordena por tamanho decrescente
    sorted_mapping = sorted(mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    for call, string_val in sorted_mapping:
        # Escapa a string adequadamente
        try:
            if '"' in string_val and "'" not in string_val:
                escaped_string = f"'{string_val}'"
            elif "'" in string_val and '"' not in string_val:
                escaped_string = f'"{string_val}"'
            else:
                escaped_string = json.dumps(string_val, ensure_ascii=False)
        except:
            escaped_string = f'"{string_val}"'
        
        # Substitui usando regex
        pattern = re.escape(call)
        old_decoded = decoded
        decoded = re.sub(pattern, escaped_string, decoded)
        
        if decoded != old_decoded:
            total_substitutions += 1
    
    print(f"🔄 Realizadas {total_substitutions} substituições nesta passada")
    return decoded

def decode_remaining_patterns(code: str) -> str:
    """Decodifica padrões restantes"""
    # Decodifica \\x
    def hex_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\x([a-fA-F0-9]{2})', hex_replacer, code)
    
    # Decodifica \\u
    def unicode_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\u([a-fA-F0-9]{4})', unicode_replacer, code)
    
    # Decodifica strings concatenadas
    code = re.sub(r'"([^"]*?)"\s*\+\s*"([^"]*?)"', r'"\1\2"', code)
    code = re.sub(r"'([^']*?)'\s*\+\s*'([^']*?)'", r"'\1\2'", code)
    
    return code

def count_remaining_obfuscation(code: str) -> int:
    """Conta quantas chamadas ofuscadas ainda restam"""
    pattern = r'_0x[a-fA-F0-9]+\s*\([^)]+\)'
    remaining = len(re.findall(pattern, code))
    return remaining

def clean_final_code(code: str) -> str:
    """Limpeza final do código"""
    # Remove funções de ofuscação
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+\s*\([^)]*\)\s*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove auto-executáveis de embaralhamento
    code = re.sub(r'\(function\s*\([^)]*\)\s*\{[^}]*while[^}]*\}[^}]*\}\s*\)\s*\([^)]*\);', '', code, flags=re.DOTALL)
    
    # Remove declarações vazias
    code = re.sub(r'(?:const|var|let)\s+_0x[a-fA-F0-9]+\s*=\s*[^;]+;', '', code)
    
    # Formata
    code = re.sub(r';\s*(?![}\]\)])', ';\n', code)
    code = re.sub(r'\{\s*(?!\})', '{\n  ', code)
    code = re.sub(r'\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = code.split('\n')
    formatted_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line == '':
            if not prev_empty:
                formatted_lines.append('')
            prev_empty = True
        else:
            formatted_lines.append(line)
            prev_empty = False
    
    return '\n'.join(formatted_lines)

def main():
    print("=== ULTIMATE DECODER - DECODIFICAÇÃO 100% ===\n")
    
    # Carrega código
    code = read_file('4.js')
    print(f"📂 Código original: {len(code):,} caracteres")
    
    # Extrai strings
    print("\n🔍 Extraindo TODOS os arrays de strings...")
    strings_array = extract_all_string_arrays_advanced(code)
    print(f"✅ Extraídas {len(strings_array)} strings únicas")
    
    if not strings_array:
        print("❌ Falha na extração de strings!")
        return
    
    # Múltiplas passadas de decodificação
    current_code = code
    pass_number = 1
    max_passes = 10
    
    while pass_number <= max_passes:
        print(f"\n🔄 === PASSADA {pass_number} ===")
        
        # Conta ofuscação restante
        remaining_before = count_remaining_obfuscation(current_code)
        print(f"📊 Chamadas ofuscadas restantes: {remaining_before}")
        
        if remaining_before == 0:
            print("🎉 Nenhuma ofuscação restante! Decode 100% completo!")
            break
        
        # Extrai funções desta passada
        functions = extract_all_function_names(current_code)
        print(f"🔧 Funções encontradas nesta passada: {len(functions)}")
        
        # Cria mapeamento
        mapping = create_ultimate_mapping(current_code, strings_array, functions)
        
        if not mapping:
            print("⚠️  Nenhum mapeamento criado nesta passada")
            break
        
        # Aplica decodificação
        current_code = apply_multiple_passes(current_code, mapping)
        
        # Decodifica padrões restantes
        current_code = decode_remaining_patterns(current_code)
        
        # Verifica progresso
        remaining_after = count_remaining_obfuscation(current_code)
        progress = remaining_before - remaining_after
        
        print(f"📈 Progresso: -{progress} chamadas ({remaining_after} restantes)")
        
        if progress == 0:
            print("⚠️  Nenhum progresso nesta passada, finalizando...")
            break
        
        pass_number += 1
    
    # Limpeza final
    print(f"\n🧹 Limpeza final...")
    final_code = clean_final_code(current_code)
    
    # Salva resultado
    print(f"\n💾 Salvando resultado final...")
    with open('codigo_100_decodificado.js', 'w', encoding='utf-8') as f:
        f.write(final_code)
    
    # Estatísticas finais
    final_remaining = count_remaining_obfuscation(final_code)
    original_calls = count_remaining_obfuscation(code)
    decode_percentage = ((original_calls - final_remaining) / original_calls * 100) if original_calls > 0 else 100
    
    print(f"\n📊 === ESTATÍSTICAS FINAIS ===")
    print(f"   • Código original: {len(code):,} caracteres")
    print(f"   • Código final: {len(final_code):,} caracteres")
    print(f"   • Passadas realizadas: {pass_number - 1}")
    print(f"   • Chamadas originais: {original_calls}")
    print(f"   • Chamadas restantes: {final_remaining}")
    print(f"   • Taxa de decode: {decode_percentage:.1f}%")
    
    if decode_percentage >= 99:
        print(f"\n🎉 SUCESSO! DECODE PRATICAMENTE 100% COMPLETO!")
    elif decode_percentage >= 90:
        print(f"\n✅ EXCELENTE! Decode {decode_percentage:.1f}% completo!")
    else:
        print(f"\n⚠️  Decode {decode_percentage:.1f}% - Algumas partes ainda ofuscadas")
    
    print(f"\n📁 Arquivo final salvo: codigo_100_decodificado.js")
    
    # Mostra prévia
    print(f"\n📄 PRÉVIA DO CÓDIGO 100% DECODIFICADO:")
    print("=" * 80)
    preview = final_code[:3000]
    print(preview)
    if len(final_code) > 3000:
        print(f"\n... (total: {len(final_code):,} caracteres)")
    print("=" * 80)

if __name__ == "__main__":
    main()
