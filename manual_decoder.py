#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Manual - Análise direta da estrutura e decode manual
"""

import re
import json

def read_file(filename: str) -> str:
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_and_analyze_structure(code: str):
    """Analisa a estrutura completa do código"""
    print("🔍 ANALISANDO ESTRUTURA COMPLETA DO CÓDIGO...")
    
    # Encontra todas as funções _0x
    functions = re.findall(r'function\s+(_0x[a-fA-F0-9]+)', code)
    print(f"📋 Funções encontradas: {functions}")
    
    # Encontra todos os padrões de chamada
    call_patterns = [
        r'_0x[a-fA-F0-9]+\([^)]+\)',
        r'_0x[a-fA-F0-9]+\(0x[a-fA-F0-9]+[^)]*\)',
        r'_0x[a-fA-F0-9]+\(-?0x[a-fA-F0-9]+[^)]*\)'
    ]
    
    all_calls = set()
    for pattern in call_patterns:
        calls = re.findall(pattern, code)
        all_calls.update(calls)
    
    print(f"📞 Total de chamadas únicas: {len(all_calls)}")
    
    # Mostra algumas chamadas de exemplo
    sample_calls = list(all_calls)[:10]
    print("📝 Exemplos de chamadas:")
    for i, call in enumerate(sample_calls):
        print(f"   {i+1}: {call}")
    
    return list(all_calls)

def extract_string_array_manual(code: str):
    """Extração manual do array de strings"""
    print("\n🔍 EXTRAÇÃO MANUAL DO ARRAY...")
    
    # Procura pelo padrão da função que contém strings
    patterns = [
        r'function\s+_0x3e50\s*\(\s*\)\s*\{[^}]*const\s+\w+\s*=\s*\[(.*?)\];',
        r'const\s+\w+\s*=\s*\[((?:[\'"][^\'\"]*[\'"](?:\s*,\s*)?){100,})\]',
        r'\[((?:[\'"][^\'\"]*[\'"](?:\s*,\s*)?){200,})\]'
    ]
    
    for i, pattern in enumerate(patterns):
        print(f"   Tentando padrão {i+1}...")
        match = re.search(pattern, code, re.DOTALL)
        if match:
            array_content = match.group(1)
            print(f"   ✅ Padrão {i+1} funcionou!")
            
            # Extrai strings
            strings = re.findall(r'[\'"]([^\'\"]*(?:\\.[^\'\"]*)*)[\'"]', array_content)
            decoded_strings = []
            
            for s in strings:
                try:
                    decoded = s.encode('utf-8').decode('unicode_escape')
                    decoded_strings.append(decoded)
                except:
                    decoded_strings.append(s)
            
            print(f"   📦 Extraídas {len(decoded_strings)} strings")
            return decoded_strings
    
    print("   ❌ Nenhum padrão funcionou")
    return []

def manual_decode_calls(code: str, strings_array: list, all_calls: list):
    """Decodificação manual das chamadas"""
    print(f"\n🔧 DECODIFICAÇÃO MANUAL DE {len(all_calls)} CHAMADAS...")
    
    mapping = {}
    successful = 0
    
    for call in all_calls:
        # Extrai o nome da função e os parâmetros
        match = re.match(r'(_0x[a-fA-F0-9]+)\(([^)]+)\)', call)
        if not match:
            continue
        
        func_name = match.group(1)
        params = match.group(2)
        
        # Tenta diferentes estratégias de decodificação
        decoded_string = None
        
        # Estratégia 1: Parâmetro hex simples
        if re.match(r'^-?0x[a-fA-F0-9]+$', params.strip()):
            try:
                index = int(params.strip(), 16)
                if 0 <= index < len(strings_array):
                    decoded_string = strings_array[index]
            except:
                pass
        
        # Estratégia 2: Expressão matemática
        elif any(op in params for op in ['+', '-', '*', '/', '%']):
            try:
                # Substitui hex por decimal
                expr = params.strip()
                hex_nums = re.findall(r'-?0x[a-fA-F0-9]+', expr)
                for hex_num in hex_nums:
                    decimal = int(hex_num, 16)
                    expr = expr.replace(hex_num, str(decimal))
                
                # Avalia a expressão
                index = eval(expr)
                if 0 <= index < len(strings_array):
                    decoded_string = strings_array[index]
            except:
                pass
        
        # Estratégia 3: Múltiplos parâmetros
        elif ',' in params:
            first_param = params.split(',')[0].strip()
            if re.match(r'^-?0x[a-fA-F0-9]+$', first_param):
                try:
                    index = int(first_param, 16)
                    if 0 <= index < len(strings_array):
                        decoded_string = strings_array[index]
                except:
                    pass
        
        # Estratégia 4: Decimal direto
        elif params.strip().lstrip('-').isdigit():
            try:
                index = int(params.strip())
                if 0 <= index < len(strings_array):
                    decoded_string = strings_array[index]
            except:
                pass
        
        if decoded_string is not None:
            mapping[call] = decoded_string
            successful += 1
    
    print(f"✅ Decodificadas {successful} de {len(all_calls)} chamadas")
    return mapping

def apply_manual_substitution(code: str, mapping: dict):
    """Aplica substituições manuais"""
    print(f"\n🔄 APLICANDO {len(mapping)} SUBSTITUIÇÕES...")
    
    decoded = code
    substitutions = 0
    
    # Ordena por tamanho decrescente
    sorted_mapping = sorted(mapping.items(), key=lambda x: len(x[0]), reverse=True)
    
    for call, string_val in sorted_mapping:
        # Escapa a string
        try:
            if '"' in string_val and "'" not in string_val:
                escaped = f"'{string_val}'"
            elif "'" in string_val and '"' not in string_val:
                escaped = f'"{string_val}"'
            else:
                escaped = json.dumps(string_val, ensure_ascii=False)
        except:
            escaped = f'"{string_val}"'
        
        # Substitui
        pattern = re.escape(call)
        old_decoded = decoded
        decoded = re.sub(pattern, escaped, decoded)
        
        if decoded != old_decoded:
            substitutions += 1
    
    print(f"✅ Realizadas {substitutions} substituições efetivas")
    return decoded

def decode_remaining_obfuscation(code: str):
    """Decodifica ofuscação restante"""
    print("\n🧹 DECODIFICANDO OFUSCAÇÃO RESTANTE...")
    
    # Decodifica \\x
    def hex_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\x([a-fA-F0-9]{2})', hex_replacer, code)
    
    # Decodifica \\u
    def unicode_replacer(match):
        try:
            return chr(int(match.group(1), 16))
        except:
            return match.group(0)
    
    code = re.sub(r'\\u([a-fA-F0-9]{4})', unicode_replacer, code)
    
    # Remove funções de ofuscação
    code = re.sub(r'function\s+_0x[a-fA-F0-9]+\s*\([^)]*\)\s*\{[^}]*\}', '', code, flags=re.DOTALL)
    
    # Remove auto-executável
    code = re.sub(r'\(function\s*\([^)]*\)\s*\{[^}]*while[^}]*\}[^}]*\}\s*\)\s*\([^)]*\);', '', code, flags=re.DOTALL)
    
    return code

def format_final_code(code: str):
    """Formata o código final"""
    print("\n✨ FORMATANDO CÓDIGO FINAL...")

    # Formata básico
    code = re.sub(r';\s*(?![}\]\)])', ';\n', code)
    code = re.sub(r'\{\s*(?!\})', '{\n  ', code)
    code = re.sub(r'\}', '\n}', code)
    
    # Remove linhas vazias excessivas
    lines = code.split('\n')
    formatted_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line == '':
            if not prev_empty:
                formatted_lines.append('')
            prev_empty = True
        else:
            formatted_lines.append(line)
            prev_empty = False
    
    return '\n'.join(formatted_lines)

def main():
    print("=== DECODER MANUAL - ANÁLISE DIRETA ===\n")
    
    # Carrega código
    code = read_file('4.js')
    print(f"📂 Código carregado: {len(code):,} caracteres")
    
    # Analisa estrutura
    all_calls = extract_and_analyze_structure(code)
    
    # Extrai strings
    strings_array = extract_string_array_manual(code)
    if not strings_array:
        print("❌ Falha na extração de strings!")
        return
    
    # Decodifica chamadas
    mapping = manual_decode_calls(code, strings_array, all_calls)
    if not mapping:
        print("❌ Falha na decodificação de chamadas!")
        return
    
    # Aplica substituições
    decoded_code = apply_manual_substitution(code, mapping)
    
    # Decodifica ofuscação restante
    cleaned_code = decode_remaining_obfuscation(decoded_code)
    
    # Formata
    final_code = format_final_code(cleaned_code)
    
    # Salva
    print("\n💾 SALVANDO CÓDIGO FINAL...")
    with open('codigo_manual_decodificado.js', 'w', encoding='utf-8') as f:
        f.write(final_code)
    
    print("✅ Código salvo em: codigo_manual_decodificado.js")
    
    # Estatísticas
    print(f"\n📊 ESTATÍSTICAS FINAIS:")
    print(f"   • Código original: {len(code):,} caracteres")
    print(f"   • Código final: {len(final_code):,} caracteres")
    print(f"   • Strings extraídas: {len(strings_array)}")
    print(f"   • Chamadas encontradas: {len(all_calls)}")
    print(f"   • Chamadas decodificadas: {len(mapping)}")
    print(f"   • Taxa de sucesso: {len(mapping)/len(all_calls)*100:.1f}%")
    
    # Prévia
    print(f"\n📄 CÓDIGO FINAL DECODIFICADO:")
    print("=" * 80)
    preview = final_code[:6000]
    print(preview)
    if len(final_code) > 6000:
        print(f"\n... (total: {len(final_code):,} caracteres)")
    print("=" * 80)
    
    print(f"\n🎉 DECODE MANUAL CONCLUÍDO!")

if __name__ == "__main__":
    main()
