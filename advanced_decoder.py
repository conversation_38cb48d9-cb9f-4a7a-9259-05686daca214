#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoder Avançado para JavaScript Ofuscado
Tenta decodificar o conteúdo real do arquivo 4.js
"""

import re
import json
from typing import List, Dict, Any, <PERSON><PERSON>

def read_file(filename: str) -> str:
    """Lê o arquivo"""
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

def extract_string_array_from_code(code: str) -> List[str]:
    """Extrai o array principal de strings do código ofuscado"""
    # Procura pelo padrão do array principal (geralmente uma função que retorna um array)
    array_pattern = r'function\s+_0x[a-fA-F0-9]+\s*\(\s*\)\s*\{\s*const\s+\w+\s*=\s*\[(.*?)\];'
    match = re.search(array_pattern, code, re.DOTALL)
    
    if match:
        array_content = match.group(1)
        # Extrai todas as strings do array
        strings = re.findall(r'[\'"]([^\'"]*)[\'"]', array_content)
        return strings
    
    # Fallback: procura por arrays grandes de strings
    large_arrays = re.findall(r'\[([\'"][^\'"]*[\'"](?:\s*,\s*[\'"][^\'"]*[\'"]){50,})\]', code)
    if large_arrays:
        strings = re.findall(r'[\'"]([^\'"]*)[\'"]', large_arrays[0])
        return strings
    
    return []

def find_decoder_function(code: str) -> str:
    """Encontra a função principal de decodificação"""
    # Procura por função que usa o array e faz operações matemáticas
    decoder_pattern = r'function\s+(_0x[a-fA-F0-9]+)\s*\([^)]+\)\s*\{[^}]*return[^}]*\[[^]]+\][^}]*\}'
    matches = re.findall(decoder_pattern, code)
    
    if matches:
        return matches[0]  # Retorna o nome da primeira função encontrada
    
    return ""

def simulate_decoder_function(strings_array: List[str], code: str) -> Dict[str, str]:
    """Simula a função de decodificação para mapear índices para strings"""
    decoded_map = {}
    
    # Procura por chamadas da função de decodificação
    call_pattern = r'_0x[a-fA-F0-9]+\s*\(\s*(0x[a-fA-F0-9]+)(?:\s*[-+]\s*0x[a-fA-F0-9]+)?\s*\)'
    calls = re.findall(call_pattern, code)
    
    for call in calls[:100]:  # Limita para evitar processamento excessivo
        try:
            index = int(call, 16)
            if 0 <= index < len(strings_array):
                decoded_map[call] = strings_array[index]
        except (ValueError, IndexError):
            continue
    
    return decoded_map

def decode_strings_in_code(code: str, string_map: Dict[str, str]) -> str:
    """Substitui as chamadas de função pelas strings decodificadas"""
    decoded_code = code
    
    # Substitui chamadas da função de decodificação
    for hex_val, string_val in string_map.items():
        # Padrão mais específico para substituição
        pattern = rf'_0x[a-fA-F0-9]+\s*\(\s*{re.escape(hex_val)}(?:\s*[-+]\s*0x[a-fA-F0-9]+)?\s*\)'
        decoded_code = re.sub(pattern, f'"{string_val}"', decoded_code)
    
    return decoded_code

def extract_meaningful_strings(strings: List[str]) -> List[str]:
    """Filtra strings que parecem ser significativas"""
    meaningful = []
    
    for s in strings:
        # Filtra strings muito curtas ou que parecem ser apenas caracteres de controle
        if len(s) >= 3 and not all(ord(c) < 32 for c in s):
            # Verifica se contém caracteres legíveis
            if any(c.isalnum() for c in s):
                meaningful.append(s)
    
    return meaningful

def analyze_decoded_content(decoded_strings: List[str]) -> Dict[str, Any]:
    """Analisa o conteúdo decodificado para identificar padrões"""
    analysis = {
        'urls': [],
        'html_tags': [],
        'css_classes': [],
        'javascript_keywords': [],
        'api_endpoints': [],
        'file_extensions': [],
        'suspicious_patterns': []
    }
    
    for string in decoded_strings:
        # URLs
        if re.match(r'https?://', string) or '.com' in string or '.org' in string:
            analysis['urls'].append(string)
        
        # Tags HTML
        if re.match(r'<[^>]+>', string) or string.startswith('<') and string.endswith('>'):
            analysis['html_tags'].append(string)
        
        # Classes CSS
        if string.startswith('.') and len(string) > 1:
            analysis['css_classes'].append(string)
        
        # Palavras-chave JavaScript
        js_keywords = ['function', 'var', 'let', 'const', 'return', 'if', 'else', 'for', 'while']
        if string in js_keywords:
            analysis['javascript_keywords'].append(string)
        
        # Endpoints de API
        if string.startswith('/api/') or string.startswith('/v1/') or 'endpoint' in string.lower():
            analysis['api_endpoints'].append(string)
        
        # Extensões de arquivo
        if re.match(r'\.\w{2,4}$', string):
            analysis['file_extensions'].append(string)
        
        # Padrões suspeitos
        suspicious = ['eval', 'exec', 'document.write', 'innerHTML', 'outerHTML', 'script']
        if any(sus in string.lower() for sus in suspicious):
            analysis['suspicious_patterns'].append(string)
    
    return analysis

def main():
    print("=== DECODER AVANÇADO PARA JAVASCRIPT OFUSCADO ===\n")
    
    # Lê o arquivo ofuscado
    code = read_file('4.js')
    print(f"Arquivo carregado: {len(code)} caracteres")
    
    # Extrai o array principal de strings
    print("\n=== EXTRAINDO ARRAY DE STRINGS ===")
    strings_array = extract_string_array_from_code(code)
    print(f"Encontradas {len(strings_array)} strings no array principal")
    
    if strings_array:
        # Mostra algumas strings de exemplo
        print("Primeiras 20 strings:")
        for i, s in enumerate(strings_array[:20]):
            print(f"  {i}: '{s}'")
        
        # Encontra strings significativas
        meaningful_strings = extract_meaningful_strings(strings_array)
        print(f"\nStrings significativas: {len(meaningful_strings)}")
        
        # Analisa o conteúdo
        print("\n=== ANÁLISE DO CONTEÚDO DECODIFICADO ===")
        analysis = analyze_decoded_content(meaningful_strings)
        
        for category, items in analysis.items():
            if items:
                print(f"\n{category.upper()}:")
                for item in items[:10]:  # Mostra até 10 itens por categoria
                    print(f"  - {item}")
                if len(items) > 10:
                    print(f"  ... e mais {len(items) - 10}")
        
        # Salva strings decodificadas
        with open('decoded_strings.json', 'w', encoding='utf-8') as f:
            json.dump({
                'total_strings': len(strings_array),
                'meaningful_strings': meaningful_strings,
                'analysis': analysis
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== STRINGS DECODIFICADAS SALVAS ===")
        print("Strings decodificadas salvas em: decoded_strings.json")
        
        # Tenta criar uma versão parcialmente decodificada do código
        print("\n=== TENTANDO DECODIFICAR O CÓDIGO ===")
        decoder_func = find_decoder_function(code)
        if decoder_func:
            print(f"Função de decodificação encontrada: {decoder_func}")
            
            # Simula a decodificação
            string_map = simulate_decoder_function(strings_array, code)
            print(f"Mapeamento criado para {len(string_map)} strings")
            
            if string_map:
                # Aplica a decodificação
                decoded_code = decode_strings_in_code(code, string_map)
                
                # Salva o código parcialmente decodificado
                with open('partially_decoded.js', 'w', encoding='utf-8') as f:
                    f.write(decoded_code)
                
                print("Código parcialmente decodificado salvo em: partially_decoded.js")
        else:
            print("Função de decodificação não encontrada")
    
    else:
        print("Não foi possível extrair o array de strings principal")

if __name__ == "__main__":
    main()
